services:
  backup-server:
    build:
      context: .
      dockerfile: Dockerfile
      network: host # 构建时使用主机网络
      args:
        http_proxy: "${http_proxy}"
        https_proxy: "${https_proxy}"
        no_proxy: "${no_proxy}"
    ports:
      - "8080:8080"
    volumes:
      - ./data:/app/data
      - ./config:/app/config
    environment:
      - TZ=Asia/Shanghai
      - http_proxy=${http_proxy}
      - https_proxy=${https_proxy}
      - HTTP_PROXY=${HTTP_PROXY}
      - HTTPS_PROXY=${HTTPS_PROXY}
      - all_proxy=${all_proxy}
      - ALL_PROXY=${ALL_PROXY}
      - no_proxy=${no_proxy}
      - NO_PROXY=${NO_PROXY}
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/api/storage/stats"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    env_file:
      - .env

volumes:
  backup_data:

networks:
  default:
    name: backup_network
